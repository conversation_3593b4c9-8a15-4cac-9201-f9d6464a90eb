# Production Environment Configuration

# Database
POSTGRES_SERVER=postgres
POSTGRES_USER=crawl_user
POSTGRES_PASSWORD=CHANGE_THIS_STRONG_PASSWORD_IN_PRODUCTION
POSTGRES_DB=crawl_db
POSTGRES_PORT=5432

# Message Broker
RABBITMQ_USER=crawl_user
RABBITMQ_PASSWORD=CHANGE_THIS_STRONG_PASSWORD_IN_PRODUCTION
RABBITMQ_VHOST=crawl_vhost
BROKER_URL=pyamqp://crawl_user:CHANGE_THIS_STRONG_PASSWORD_IN_PRODUCTION@rabbitmq:5672/crawl_vhost
RESULT_BACKEND=redis://redis:6379/0

# Application
DEBUG=false
LOG_LEVEL=INFO
API_V1_PREFIX=/api/v1

# Crawling
USER_AGENT=CrawlBot/1.0 (+https://yourcompany.com/bot)
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=60
MAX_CONCURRENT_REQUESTS=10
RESPECT_ROBOTS_TXT=true

# Rate Limiting
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60

# Security
SECRET_KEY=CHANGE_THIS_TO_A_STRONG_SECRET_KEY_IN_PRODUCTION
