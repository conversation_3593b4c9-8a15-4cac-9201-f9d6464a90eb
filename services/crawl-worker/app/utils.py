"""Utility functions for crawl worker."""

import hashlib
import logging
from typing import Dict, Any, List, Optional
from urllib.parse import urljoin, urlparse
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

logger = logging.getLogger(__name__)


def calculate_content_hash(content: str) -> str:
    """Calculate SHA-256 hash of content for deduplication."""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()


def extract_domain(url: str) -> str:
    """Extract domain from URL."""
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower()
    except Exception as e:
        logger.error(f"Failed to extract domain from {url}: {e}")
        return ""


def normalize_url(url: str, base_url: str = None) -> str:
    """Normalize and resolve relative URLs."""
    try:
        if base_url:
            url = urljoin(base_url, url)
        
        parsed = urlparse(url)
        # Remove fragment and normalize
        normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        if parsed.query:
            normalized += f"?{parsed.query}"
        
        return normalized
    except Exception as e:
        logger.error(f"Failed to normalize URL {url}: {e}")
        return url


def extract_links_from_content(content: str, base_url: str) -> List[str]:
    """Extract and normalize links from HTML content."""
    try:
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(content, 'html.parser')
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            if href.startswith(('http://', 'https://')):
                links.append(href)
            elif href.startswith('/'):
                links.append(urljoin(base_url, href))
        
        # Remove duplicates and filter same domain
        base_domain = extract_domain(base_url)
        same_domain_links = []
        
        for link in set(links):
            if extract_domain(link) == base_domain:
                same_domain_links.append(normalize_url(link))
        
        return same_domain_links
        
    except Exception as e:
        logger.error(f"Failed to extract links from content: {e}")
        return []


def create_extraction_strategy(config: Dict[str, Any]) -> JsonCssExtractionStrategy:
    """Create extraction strategy from configuration."""
    try:
        schema = config.get("schema", {})
        return JsonCssExtractionStrategy(schema)
    except Exception as e:
        logger.error(f"Failed to create extraction strategy: {e}")
        # Return default strategy
        default_schema = {
            "name": "Default",
            "baseSelector": "body",
            "fields": [
                {"name": "title", "selector": "h1, title", "type": "text"},
                {"name": "content", "selector": "p, article", "type": "text"}
            ]
        }
        return JsonCssExtractionStrategy(default_schema)


def validate_url(url: str) -> bool:
    """Validate URL format."""
    try:
        parsed = urlparse(url)
        return bool(parsed.scheme and parsed.netloc)
    except Exception:
        return False


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage."""
    import re
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return sanitized[:255]  # Limit length


def get_robots_txt_url(url: str) -> str:
    """Get robots.txt URL for a given URL."""
    try:
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}/robots.txt"
    except Exception:
        return None


def is_allowed_by_robots(url: str, user_agent: str = "*") -> bool:
    """Check if URL is allowed by robots.txt (simplified check)."""
    try:
        import urllib.robotparser
        
        robots_url = get_robots_txt_url(url)
        if not robots_url:
            return True
        
        rp = urllib.robotparser.RobotFileParser()
        rp.set_url(robots_url)
        rp.read()
        
        return rp.can_fetch(user_agent, url)
    except Exception as e:
        logger.warning(f"Failed to check robots.txt for {url}: {e}")
        return True  # Allow by default if check fails
