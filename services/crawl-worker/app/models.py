"""Database models for Crawl Worker Service."""

from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from pydantic import BaseModel, HttpUrl
from enum import Enum


Base = declarative_base()


class LinkStatus(str, Enum):
    """Link processing status enumeration."""
    PENDING = "pending"
    CRAWLING = "crawling"
    COMPLETED = "completed"
    FAILED = "failed"


class SiteConfig(Base):
    """Site-specific crawling configurations."""
    __tablename__ = "site_configs"
    
    domain = Column(String, primary_key=True)
    config = Column(JSON, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class Link(Base):
    """Discovered links queue and status tracking."""
    __tablename__ = "links"
    
    id = Column(Integer, primary_key=True, index=True)
    url = Column(Text, unique=True, nullable=False, index=True)
    domain = Column(String, nullable=False, index=True)
    link_metadata = Column(JSON, default={})
    status = Column(String, default=LinkStatus.PENDING, index=True)
    priority = Column(Integer, default=0, index=True)
    retry_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class Page(Base):
    """Crawled page content storage."""
    __tablename__ = "pages"
    
    id = Column(Integer, primary_key=True, index=True)
    url = Column(Text, unique=True, nullable=False, index=True)
    title = Column(Text)
    content = Column(JSON, nullable=False)
    extracted_data = Column(JSON, default={})
    crawled_at = Column(DateTime(timezone=True), server_default=func.now())
    content_hash = Column(String, index=True)
    status_code = Column(Integer)


# Pydantic models for task processing
class CrawlResult(BaseModel):
    """Schema for crawl task result."""
    url: str
    status_code: int
    title: Optional[str] = None
    content: Dict[str, Any]
    extracted_data: Dict[str, Any] = {}
    content_hash: str
    links_found: int = 0
    processing_time: float


class TaskStatus(BaseModel):
    """Schema for task status."""
    task_id: str
    status: str
    url: str
    retry_count: int
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
