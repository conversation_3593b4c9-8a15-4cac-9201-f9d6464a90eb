"""Configuration management for Crawl Worker Service."""

from typing import Optional, Dict, Any
from pydantic import PostgresDsn, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    app_name: str = "Crawl Worker Service"
    debug: bool = False
    log_level: str = "INFO"
    
    # Database
    postgres_server: str
    postgres_user: str
    postgres_password: str
    postgres_db: str
    postgres_port: str = "5432"
    database_url: Optional[str] = None
    
    @field_validator("database_url", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> str:
        if isinstance(v, str):
            return v
        values = info.data if hasattr(info, 'data') else {}
        port = values.get("postgres_port")
        if port is not None:
            port = int(port)
        dsn = PostgresDsn.build(
            scheme="postgresql+asyncpg",
            username=values.get("postgres_user"),
            password=values.get("postgres_password"),
            host=values.get("postgres_server"),
            port=port,
            path=f"/{values.get('postgres_db') or ''}",
        )
        return str(dsn)
    
    # Celery/Redis
    broker_url: str
    result_backend: str
    
    # Crawling settings
    user_agent: str = "CrawlBot/1.0 (+https://example.com/bot)"
    request_timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 60
    max_concurrent_requests: int = 10
    respect_robots_txt: bool = True
    
    # Content extraction
    max_content_size: int = 10 * 1024 * 1024  # 10MB
    extract_images: bool = True
    extract_links: bool = True
    
    # Default extraction configuration
    default_extraction_config: Dict[str, Any] = {
        "schema": {
            "name": "Default Schema",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "title",
                    "selector": "h1, title",
                    "type": "text"
                },
                {
                    "name": "content",
                    "selector": "p, article, .content, main",
                    "type": "text"
                },
                {
                    "name": "links",
                    "selector": "a[href]",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "images",
                    "selector": "img[src]",
                    "type": "attribute",
                    "attribute": "src"
                }
            ]
        }
    }
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
