"""Health check endpoints."""

import logging
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from celery import Celery
import asyncio
from ..database import get_db
from ..config import settings

logger = logging.getLogger(__name__)
router = APIRouter()


async def check_rabbitmq_health() -> str:
    """Check RabbitMQ connection health via Celery."""
    try:
        # Create a temporary Celery app to test broker connection
        celery_app = Celery('health_check')
        celery_app.conf.broker_url = settings.broker_url
        celery_app.conf.result_backend = settings.result_backend

        # Try to get broker connection info
        inspect = celery_app.control.inspect()
        # This will raise an exception if broker is not accessible
        stats = inspect.stats()

        if stats:
            return "healthy"
        else:
            return "unhealthy"
    except Exception as e:
        logger.error(f"RabbitMQ health check failed: {e}")
        return "unhealthy"


async def check_celery_workers() -> Dict[str, Any]:
    """Check Celery worker status."""
    try:
        # Create Celery app instance for inspection
        celery_app = Celery('health_check')
        celery_app.conf.broker_url = settings.broker_url
        celery_app.conf.result_backend = settings.result_backend

        # Get active workers
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()

        if active_workers:
            worker_count = len(active_workers)
            return {
                "status": "healthy",
                "worker_count": worker_count,
                "workers": list(active_workers.keys())
            }
        else:
            return {
                "status": "unhealthy",
                "worker_count": 0,
                "workers": []
            }
    except Exception as e:
        logger.error(f"Celery worker health check failed: {e}")
        return {
            "status": "unhealthy",
            "worker_count": 0,
            "workers": [],
            "error": str(e)
        }


@router.get("/health")
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    Health check endpoint.

    Returns:
        dict: Health status information
    """
    try:
        # Check database connectivity
        result = await db.execute(text("SELECT 1"))
        db_status = "healthy" if result else "unhealthy"

        # Check RabbitMQ connectivity
        queue_status = await check_rabbitmq_health()

        # Check Celery workers
        worker_info = await check_celery_workers()

        # Determine overall status
        overall_status = "healthy"
        if db_status != "healthy" or queue_status != "healthy" or worker_info["status"] != "healthy":
            overall_status = "unhealthy"

        return {
            "status": overall_status,
            "timestamp": datetime.utcnow().isoformat(),
            "service": "link-discovery",
            "version": "1.0.0",
            "database": db_status,
            "queue": queue_status,
            "workers": worker_info
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@router.get("/health/ready")
async def readiness_check(db: AsyncSession = Depends(get_db)):
    """
    Readiness check endpoint.
    
    Returns:
        dict: Readiness status
    """
    try:
        # Check if service is ready to accept requests
        await db.execute(text("SELECT COUNT(*) FROM site_configs"))
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail="Service not ready")


@router.get("/health/live")
async def liveness_check():
    """
    Liveness check endpoint.
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat()
    }
