"""WebSocket endpoints for real-time updates."""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from ..database import get_db, AsyncSessionLocal
from ..models import Link, Page, SiteConfig, LinkStatus

logger = logging.getLogger(__name__)
router = APIRouter()

# Store active WebSocket connections
active_connections: Set[WebSocket] = set()


class ConnectionManager:
    """Manages WebSocket connections and broadcasts."""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
    
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        self.active_connections.discard(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Failed to send personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """Broadcast a message to all connected WebSockets."""
        if not self.active_connections:
            return
        
        disconnected = set()
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Failed to broadcast to connection: {e}")
                disconnected.add(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def broadcast_json(self, data: Dict[str, Any]):
        """Broadcast JSON data to all connected WebSockets."""
        message = json.dumps(data, default=str)
        await self.broadcast(message)


# Global connection manager
manager = ConnectionManager()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time updates.

    Provides real-time updates for:
    - Link status changes
    - Dashboard statistics
    - System health
    - Notifications
    """
    await manager.connect(websocket)

    try:
        # Send initial data after connection is established
        await send_initial_data(websocket)

        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (ping/pong, subscriptions, etc.)
                data = await websocket.receive_text()
                message = json.loads(data)

                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }))
                elif message.get("type") == "subscribe":
                    # Handle subscription requests
                    await handle_subscription(websocket, message)

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logger.warning("Received invalid JSON from WebSocket client")
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                break

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        manager.disconnect(websocket)


async def send_initial_data(websocket: WebSocket):
    """Send initial dashboard data to a newly connected client."""
    try:
        # Send a simple welcome message first
        await websocket.send_text(json.dumps({
            "type": "connected",
            "message": "WebSocket connected successfully",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }))

        # Try to get database session and send stats
        try:
            async with AsyncSessionLocal() as db:
                # Get current statistics
                stats = await get_dashboard_stats(db)

                # Send initial stats
                await websocket.send_text(json.dumps({
                    "type": "stats_update",
                    "data": stats,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))

                # Send system health
                health = await get_system_health(db)
                await websocket.send_text(json.dumps({
                    "type": "system_health",
                    "data": health,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))

        except Exception as db_error:
            logger.error(f"Database error in send_initial_data: {db_error}")
            # Send error notification to client
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Failed to load initial data",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }))

    except Exception as e:
        logger.error(f"Failed to send initial data: {e}")


async def handle_subscription(websocket: WebSocket, message: Dict[str, Any]):
    """Handle subscription requests from clients."""
    subscription_type = message.get("subscription")
    
    if subscription_type == "link_updates":
        # Client wants to receive link updates
        await websocket.send_text(json.dumps({
            "type": "subscription_confirmed",
            "subscription": "link_updates",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }))
    elif subscription_type == "stats_updates":
        # Client wants to receive stats updates
        await websocket.send_text(json.dumps({
            "type": "subscription_confirmed",
            "subscription": "stats_updates",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }))


async def get_dashboard_stats(db: AsyncSession) -> Dict[str, Any]:
    """Get current dashboard statistics."""
    try:
        # Count links by status
        total_links = await db.scalar(select(func.count(Link.id)))
        pending_links = await db.scalar(
            select(func.count(Link.id)).where(Link.status == LinkStatus.PENDING)
        )
        crawling_links = await db.scalar(
            select(func.count(Link.id)).where(Link.status == LinkStatus.CRAWLING)
        )
        completed_links = await db.scalar(
            select(func.count(Link.id)).where(Link.status == LinkStatus.COMPLETED)
        )
        failed_links = await db.scalar(
            select(func.count(Link.id)).where(Link.status == LinkStatus.FAILED)
        )
        
        # Count unique domains
        total_domains = await db.scalar(select(func.count(func.distinct(Link.domain))))
        
        # Calculate success rate
        success_rate = (completed_links / total_links * 100) if total_links > 0 else 0
        
        return {
            "totalLinks": total_links or 0,
            "pendingLinks": pending_links or 0,
            "crawlingLinks": crawling_links or 0,
            "completedLinks": completed_links or 0,
            "failedLinks": failed_links or 0,
            "totalDomains": total_domains or 0,
            "successRate": round(success_rate, 2),
            "averageProcessingTime": 0  # TODO: Calculate from actual data
        }
        
    except Exception as e:
        logger.error(f"Failed to get dashboard stats: {e}")
        return {
            "totalLinks": 0,
            "pendingLinks": 0,
            "crawlingLinks": 0,
            "completedLinks": 0,
            "failedLinks": 0,
            "totalDomains": 0,
            "successRate": 0,
            "averageProcessingTime": 0
        }


async def get_system_health(db: AsyncSession) -> Dict[str, Any]:
    """Get current system health status."""
    try:
        # Import health check functions
        from .health import check_rabbitmq_health, check_celery_workers

        # Check database connectivity
        await db.execute(select(1))
        db_status = "healthy"

        # Check RabbitMQ
        queue_status = await check_rabbitmq_health()

        # Check Celery workers
        worker_info = await check_celery_workers()

        return {
            "status": "healthy",
            "database": db_status,
            "queue": queue_status,
            "workers": worker_info["status"],
            "worker_count": worker_info.get("worker_count", 0),
            "worker_list": worker_info.get("workers", [])
        }

    except Exception as e:
        logger.error(f"Failed to get system health: {e}")
        return {
            "status": "unhealthy",
            "database": "unhealthy",
            "queue": "unhealthy",
            "workers": "unhealthy",
            "worker_count": 0,
            "worker_list": []
        }


# Functions to broadcast updates (to be called from other parts of the application)
async def broadcast_link_update(link_data: Dict[str, Any]):
    """Broadcast link status update to all connected clients."""
    await manager.broadcast_json({
        "type": "link_update",
        "data": link_data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    })


async def broadcast_stats_update(stats_data: Dict[str, Any]):
    """Broadcast statistics update to all connected clients."""
    await manager.broadcast_json({
        "type": "stats_update",
        "data": stats_data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    })


async def broadcast_notification(notification: Dict[str, Any]):
    """Broadcast notification to all connected clients."""
    await manager.broadcast_json({
        "type": "notification",
        "data": notification,
        "timestamp": datetime.now(timezone.utc).isoformat()
    })
