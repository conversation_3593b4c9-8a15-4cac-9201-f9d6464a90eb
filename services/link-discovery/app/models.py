"""Database models for Link Discovery Service."""

from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, JSON, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from pydantic import BaseModel, HttpUrl, field_validator, ConfigDict
from enum import Enum


Base = declarative_base()


class LinkStatus(str, Enum):
    """Link processing status enumeration."""
    PENDING = "pending"
    CRAWLING = "crawling"
    COMPLETED = "completed"
    FAILED = "failed"


class SiteConfig(Base):
    """Site-specific crawling configurations."""
    __tablename__ = "site_configs"
    
    domain = Column(String, primary_key=True)
    config = Column(JSON, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class Link(Base):
    """Discovered links queue and status tracking."""
    __tablename__ = "links"

    id = Column(Integer, primary_key=True, index=True)
    url = Column(Text, unique=True, nullable=False, index=True)
    domain = Column(String, nullable=False, index=True)
    link_metadata = Column("metadata", JSON, default={})
    status = Column(String, default=LinkStatus.PENDING, index=True)
    priority = Column(Integer, default=0, index=True)
    retry_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class Page(Base):
    """Crawled page content storage."""
    __tablename__ = "pages"
    
    id = Column(Integer, primary_key=True, index=True)
    url = Column(Text, unique=True, nullable=False, index=True)
    title = Column(Text)
    content = Column(JSON, nullable=False)
    extracted_data = Column(JSON, default={})
    crawled_at = Column(DateTime(timezone=True), server_default=func.now())
    content_hash = Column(String, index=True)
    status_code = Column(Integer)


# Pydantic models for API
class SiteConfigCreate(BaseModel):
    """Schema for creating site configuration."""
    domain: str
    config: Dict[str, Any]
    
    @field_validator('domain')
    @classmethod
    def validate_domain(cls, v):
        if not v or '.' not in v:
            raise ValueError('Invalid domain format')
        return v.lower()


class SiteConfigResponse(BaseModel):
    """Schema for site configuration response."""
    domain: str
    config: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class LinkCreate(BaseModel):
    """Schema for creating link."""
    url: HttpUrl
    domain: Optional[str] = None
    link_metadata: Dict[str, Any] = {}
    priority: int = 0


class LinkResponse(BaseModel):
    """Schema for link response."""
    id: int
    url: str
    domain: str
    link_metadata: Dict[str, Any]
    status: LinkStatus
    priority: int
    retry_count: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class DiscoveryRequest(BaseModel):
    """Schema for link discovery request."""
    urls: list[HttpUrl]
    max_depth: int = 2
    site_config: Optional[Dict[str, Any]] = None
    priority: int = 0


class DiscoveryResponse(BaseModel):
    """Schema for discovery response."""
    discovered_links: int
    queued_tasks: int
    message: str
