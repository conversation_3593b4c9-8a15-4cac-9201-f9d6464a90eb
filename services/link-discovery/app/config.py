"""Configuration management for Link Discovery Service."""

from typing import Optional
from pydantic import PostgresDsn, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    app_name: str = "Link Discovery Service"
    debug: bool = False
    log_level: str = "INFO"
    
    # API
    api_v1_prefix: str = "/api/v1"
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Database
    postgres_server: str
    postgres_user: str
    postgres_password: str
    postgres_db: str
    postgres_port: str = "5432"
    database_url: Optional[str] = None
    
    @field_validator("database_url", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> str:
        if isinstance(v, str):
            return v
        values = info.data if hasattr(info, 'data') else {}
        port = values.get("postgres_port")
        if port is not None:
            port = int(port)
        dsn = PostgresDsn.build(
            scheme="postgresql+asyncpg",
            username=values.get("postgres_user"),
            password=values.get("postgres_password"),
            host=values.get("postgres_server"),
            port=port,
            path=values.get('postgres_db') or '',
        )
        return str(dsn)
    
    # Redis/RabbitMQ
    broker_url: str
    result_backend: str
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60
    
    # Crawling
    default_max_depth: int = 2
    default_delay: float = 1.0
    max_concurrent_requests: int = 10
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
