# Scalable Web Crawler Service

A production-ready, scalable web crawler microservices architecture built with FastAPI, Celery, and Crawl4AI.

## Architecture Overview

This system consists of two main microservices:

1. **Link Discovery Service** - FastAPI-based REST API for managing crawl requests and site configurations
2. **Crawl Worker Service** - Celery-based distributed workers for performing actual web crawling

## Features

- **Scalable Architecture**: Microservices design with horizontal scaling capabilities
- **Async Web Crawling**: Built on Crawl4AI for high-performance content extraction
- **Distributed Task Processing**: Celery with RabbitMQ for reliable task queuing
- **Content Deduplication**: SHA-256 hash-based duplicate detection
- **Site-Specific Configuration**: JSON-based CSS extraction rules per domain
- **Comprehensive Monitoring**: Health checks, logging, and Celery monitoring via Flower
- **Production Ready**: Docker containerization with development and production configurations

## Technology Stack

- **FastAPI** - Modern, fast web framework for building APIs
- **Crawl4AI** - Advanced web crawling and content extraction
- **Celery** - Distributed task queue for background processing
- **RabbitMQ** - Message broker with management interface
- **PostgreSQL** - Primary database with async SQLAlchemy ORM
- **Redis** - Celery result backend
- **Docker & Docker Compose** - Containerization and orchestration

## Quick Start

### Development Environment

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd crawl
   cp .env.dev .env
   ```

2. **Start services**:
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **Access services**:
   - API Documentation: http://localhost:8000/api/v1/docs
   - RabbitMQ Management: http://localhost:15672 (guest/guest)
   - Flower (Celery Monitor): http://localhost:5555

### Production Environment

1. **Configure environment**:
   ```bash
   cp .env.prod .env
   # Edit .env with production values
   ```

2. **Deploy**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

## API Endpoints

### Link Discovery Service

#### POST /api/v1/discover
Submit URLs for crawling:
```json
{
  "urls": ["https://example.com", "https://example.com/page"],
  "max_depth": 2,
  "priority": 0,
  "site_config": {
    "schema": {
      "name": "Custom Schema",
      "baseSelector": "body",
      "fields": [
        {"name": "title", "selector": "h1", "type": "text"},
        {"name": "content", "selector": ".content", "type": "text"}
      ]
    }
  }
}
```

#### GET /api/v1/sites/{domain}/config
Retrieve site-specific extraction configuration.

#### PUT /api/v1/sites/{domain}/config
Update site-specific extraction configuration.

#### GET /api/v1/links
List discovered links with filtering:
- `domain`: Filter by domain
- `status`: Filter by status (pending, crawling, completed, failed)
- `limit`: Number of results (default: 100)
- `offset`: Pagination offset

#### GET /health
Service health check endpoint.

## Database Schema

### Tables

- **site_configs**: Domain-specific crawling configurations
- **links**: Discovered URLs queue with status tracking
- **pages**: Crawled content storage with extracted data

### Indexes

Optimized indexes for:
- URL lookups
- Domain filtering
- Status queries
- Priority ordering
- Content hash deduplication

## Configuration

### Environment Variables

#### Database
- `POSTGRES_SERVER`: Database host
- `POSTGRES_USER`: Database username
- `POSTGRES_PASSWORD`: Database password
- `POSTGRES_DB`: Database name

#### Message Broker
- `BROKER_URL`: RabbitMQ connection URL
- `RESULT_BACKEND`: Redis connection URL

#### Crawling
- `USER_AGENT`: Bot user agent string
- `REQUEST_TIMEOUT`: HTTP request timeout (seconds)
- `MAX_RETRIES`: Maximum retry attempts
- `RESPECT_ROBOTS_TXT`: Honor robots.txt (true/false)

### Site Configuration Format

```json
{
  "schema": {
    "name": "Site Schema Name",
    "baseSelector": "body",
    "fields": [
      {
        "name": "title",
        "selector": "h1, .title",
        "type": "text"
      },
      {
        "name": "content",
        "selector": ".content, article",
        "type": "text"
      },
      {
        "name": "links",
        "selector": "a[href]",
        "type": "attribute",
        "attribute": "href"
      }
    ]
  },
  "crawl_delay": 1.0,
  "max_depth": 3,
  "follow_external_links": false
}
```

## Monitoring

### Health Checks

- **Link Discovery**: `GET /health`
- **Database**: Connection and query tests
- **Worker Health**: Celery task monitoring

### Logging

Structured JSON logging with:
- Correlation IDs for request tracing
- Error tracking and stack traces
- Performance metrics

### Metrics

- Crawl success/failure rates
- Processing times
- Queue depths
- Worker status

## Scaling

### Horizontal Scaling

1. **API Service**: Scale via Docker Compose replicas
2. **Workers**: Increase worker replicas for higher throughput
3. **Database**: Configure read replicas for query scaling

### Performance Tuning

- Adjust `worker_prefetch_multiplier` for memory optimization
- Configure `max_concurrent_requests` per worker
- Tune database connection pools

## Security

- Input validation and sanitization
- Rate limiting on API endpoints
- Secure credential management
- Network isolation in production

## Development

### Local Development

1. **Install dependencies**:
   ```bash
   cd services/link-discovery
   pip install -r requirements.txt
   
   cd ../crawl-worker
   pip install -r requirements.txt
   ```

2. **Run services locally**:
   ```bash
   # Terminal 1: Start dependencies
   docker-compose -f docker-compose.dev.yml up postgres rabbitmq redis
   
   # Terminal 2: Start API
   cd services/link-discovery
   uvicorn app.main:app --reload
   
   # Terminal 3: Start worker
   cd services/crawl-worker
   celery -A app.tasks worker --loglevel=info
   ```

### Testing

```bash
# Run API tests
cd services/link-discovery
pytest

# Run worker tests
cd services/crawl-worker
pytest

# Integration tests
pytest tests/integration/
```

## Troubleshooting

### Common Issues

1. **Database Connection**: Check PostgreSQL service status and credentials
2. **Message Broker**: Verify RabbitMQ is running and accessible
3. **Worker Tasks**: Monitor Celery logs for task failures
4. **Memory Usage**: Adjust worker concurrency for available resources

### Logs

```bash
# View service logs
docker-compose logs -f link-discovery
docker-compose logs -f crawl-worker

# Database logs
docker-compose logs postgres

# Message broker logs
docker-compose logs rabbitmq
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request

## Support

For issues and questions:
- Create GitHub issues for bugs
- Check documentation for configuration help
- Review logs for troubleshooting
