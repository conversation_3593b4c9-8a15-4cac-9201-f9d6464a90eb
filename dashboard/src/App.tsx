import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import Links from './pages/Links';
import Analytics from './pages/Analytics';
import Configs from './pages/Configs';
import Health from './pages/Health';
import Storage from './pages/Storage';

function App() {
  return (
    <ThemeProvider>
      <Router>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="links" element={<Links />} />
            <Route path="analytics" element={<Analytics />} />
            <Route path="configs" element={<Configs />} />
            <Route path="health" element={<Health />} />
            <Route path="storage" element={<Storage />} />
          </Route>
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
