// Dashboard-specific types and interfaces

export interface DashboardStats {
  totalLinks: number;
  pendingLinks: number;
  crawlingLinks: number;
  completedLinks: number;
  failedLinks: number;
  totalDomains: number;
  successRate: number;
  averageProcessingTime: number;
}

export interface ChartDataPoint {
  timestamp: string;
  value: number;
  label?: string;
}

export interface TimeSeriesData {
  name: string;
  data: ChartDataPoint[];
  color?: string;
}

export interface DomainStats {
  domain: string;
  totalLinks: number;
  completedLinks: number;
  failedLinks: number;
  successRate: number;
}

export interface CrawlingMetrics {
  linksPerHour: number;
  averageResponseTime: number;
  errorRate: number;
  queueDepth: number;
  activeWorkers: number;
}

export interface SystemHealth {
  api: 'healthy' | 'unhealthy' | 'unknown';
  database: 'healthy' | 'unhealthy' | 'unknown';
  workers: 'healthy' | 'unhealthy' | 'unknown';
  queue: 'healthy' | 'unhealthy' | 'unknown';
}

export interface NotificationMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

export interface FilterOptions {
  domains: string[];
  statuses: string[];
  dateRange: {
    start: string;
    end: string;
  };
}

export interface TableColumn {
  id: string;
  label: string;
  sortable: boolean;
  width?: number;
  align?: 'left' | 'center' | 'right';
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

export interface ThemeMode {
  mode: 'light' | 'dark';
}

// Real-time update types
export interface WebSocketMessage {
  type: 'link_update' | 'stats_update' | 'system_health' | 'notification';
  data: any;
  timestamp: string;
}

export interface RealTimeUpdate {
  linkUpdates: Link[];
  statsUpdate: Partial<DashboardStats>;
  systemHealth: Partial<SystemHealth>;
  notifications: NotificationMessage[];
}
