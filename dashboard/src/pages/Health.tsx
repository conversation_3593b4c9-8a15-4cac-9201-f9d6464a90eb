import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useRealTimeHealth } from '../hooks/useApi';

const Health: React.FC = () => {
  const { data: health, loading, error } = useRealTimeHealth(10000);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading health data: {error}
      </Alert>
    );
  }

  const getStatusColor = (status: string) => {
    return status === 'healthy' ? 'success' : 'error';
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        System Health
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Service Status
              </Typography>
              {health && (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Typography variant="body1">Overall Status:</Typography>
                    <Chip
                      label={health.status}
                      color={getStatusColor(health.status)}
                      variant="outlined"
                    />
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Typography variant="body1">Database:</Typography>
                    <Chip
                      label={health.database}
                      color={getStatusColor(health.database)}
                      variant="outlined"
                    />
                  </Box>

                  <Typography variant="body2" color="textSecondary">
                    Service: {health.service} v{health.version}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Last updated: {new Date(health.timestamp).toLocaleString()}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Additional Metrics
              </Typography>
              <Typography variant="body1" color="textSecondary">
                Additional health metrics and monitoring data will be displayed here.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Health;
