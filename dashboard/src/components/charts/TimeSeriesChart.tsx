import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON>xis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { Box, Typography, Card, CardContent, useTheme } from '@mui/material';
import type { TimeSeriesData } from '../../types/dashboard';

interface TimeSeriesChartProps {
  title: string;
  data: TimeSeriesData[];
  height?: number;
  type?: 'line' | 'area';
  showLegend?: boolean;
  showGrid?: boolean;
  xAxisKey?: string;
  yAxisLabel?: string;
  loading?: boolean;
}

const TimeSeriesChart: React.FC<TimeSeriesChartProps> = ({
  title,
  data,
  height = 300,
  type = 'line',
  showLegend = true,
  showGrid = true,
  xAxisKey = 'timestamp',
  yAxisLabel,
  loading = false,
}) => {
  const theme = useTheme();

  // Combine all data series into a single dataset for the chart
  const chartData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    // Get all unique timestamps
    const timestamps = new Set<string>();
    data.forEach(series => {
      series.data.forEach(point => timestamps.add(point.timestamp));
    });

    // Create combined data points
    return Array.from(timestamps)
      .sort()
      .map(timestamp => {
        const point: { [key: string]: string | number } = { [xAxisKey]: timestamp };

        data.forEach(series => {
          const dataPoint = series.data.find(p => p.timestamp === timestamp);
          point[series.name] = dataPoint?.value || 0;
        });

        return point;
      });
  }, [data, xAxisKey]);

  const colors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
  ];

  interface Entry {
    name: string;
    value: number;
    color: string;
  }

  interface CustomTooltipProps {
    active?: boolean;
    payload?: Entry[];
    label?: string | number;
  }

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            p: 1,
            boxShadow: 2,
          }}
        >
          <Typography variant="body2" sx={{ mb: 1 }}>
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" sx={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Box
            sx={{
              height,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'text.secondary',
            }}
          >
            Loading chart data...
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0 || chartData.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Box
            sx={{
              height,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'text.secondary',
            }}
          >
            No data available
          </Box>
        </CardContent>
      </Card>
    );
  }

  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    if (type === 'area') {
      return (
        <AreaChart {...commonProps}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />}
          <XAxis dataKey={xAxisKey} stroke={theme.palette.text.secondary} fontSize={12} />
          <YAxis
            stroke={theme.palette.text.secondary}
            fontSize={12}
            label={
              yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined
            }
          />
          <Tooltip content={<CustomTooltip />} />
          {showLegend && <Legend />}
          {data.map((series, index) => (
            <Area
              key={series.name}
              type="monotone"
              dataKey={series.name}
              stackId="1"
              stroke={series.color || colors[index % colors.length]}
              fill={series.color || colors[index % colors.length]}
              fillOpacity={0.6}
            />
          ))}
        </AreaChart>
      );
    }

    return (
      <LineChart {...commonProps}>
        {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />}
        <XAxis dataKey={xAxisKey} stroke={theme.palette.text.secondary} fontSize={12} />
        <YAxis
          stroke={theme.palette.text.secondary}
          fontSize={12}
          label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
        />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && <Legend />}
        {data.map((series, index) => (
          <Line
            key={series.name}
            type="monotone"
            dataKey={series.name}
            stroke={series.color || colors[index % colors.length]}
            strokeWidth={2}
            dot={{ r: 4 }}
            activeDot={{ r: 6 }}
          />
        ))}
      </LineChart>
    );
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <ResponsiveContainer width="100%" height={height}>
          {renderChart()}
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default TimeSeriesChart;
