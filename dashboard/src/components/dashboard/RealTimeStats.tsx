import React, { useState, useEffect } from 'react';
import { Grid, Box, Typography, Card, CardContent, LinearProgress, Alert } from '@mui/material';
import {
  Link as LinkIcon,
  CheckCircle,
  Schedule,
  Error,
  Speed,
  Storage,
  TrendingUp,
  Assessment,
} from '@mui/icons-material';
import StatCard from '../common/StatCard';
import StatusIndicator from '../common/StatusIndicator';
import { useDashboardStats, useRealTimeHealth } from '../../hooks/useApi';
import { useRealTimeLinkUpdates, useRealTimeStatsUpdates } from '../../hooks/useWebSocket';

const RealTimeStats: React.FC = () => {
  const { data: stats, loading: statsLoading, error: statsError, refetch } = useDashboardStats();
  const { data: health, loading: healthLoading } = useRealTimeHealth(30000);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Real-time updates via WebSocket
  useRealTimeLinkUpdates(linkUpdates => {
    console.log('Received link updates:', linkUpdates);
    refetch(); // Refresh stats when links are updated
    setLastUpdate(new Date());
  });

  useRealTimeStatsUpdates(statsUpdate => {
    console.log('Received stats update:', statsUpdate);
    setLastUpdate(new Date());
  });

  // Auto-refresh every 30 seconds as fallback
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
      setLastUpdate(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, [refetch]);

  if (statsError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading statistics: {statsError}
      </Alert>
    );
  }

  const getSuccessRate = () => {
    if (!stats) return 0;
    const total = stats.completedLinks + stats.failedLinks;
    return total > 0 ? (stats.completedLinks / total) * 100 : 0;
  };

  const getProcessingRate = () => {
    if (!stats) return 0;
    const total = stats.totalLinks;
    const processed = stats.completedLinks + stats.failedLinks;
    return total > 0 ? (processed / total) * 100 : 0;
  };

  return (
    <Box>
      {/* Header with last update time */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Real-time Statistics
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <StatusIndicator
            status={health?.status === 'healthy' ? 'healthy' : 'unhealthy'}
            label="System"
            description={`API Status: ${health?.status || 'unknown'}`}
            variant="chip"
            animate={healthLoading}
          />
          <Typography variant="caption" color="textSecondary">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </Typography>
        </Box>
      </Box>

      {/* Main Statistics Grid */}
      <Grid container spacing={3}>
        {/* Total Links */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Links"
            value={stats?.totalLinks || 0}
            subtitle="Discovered links"
            icon={<LinkIcon />}
            color="primary"
            loading={statsLoading}
          />
        </Grid>

        {/* Completed Links */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Completed"
            value={stats?.completedLinks || 0}
            subtitle={`${getSuccessRate().toFixed(1)}% success rate`}
            icon={<CheckCircle />}
            color="success"
            loading={statsLoading}
            trend="up"
            trendValue={`${stats?.completedLinks || 0} processed`}
          />
        </Grid>

        {/* Pending Links */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending"
            value={stats?.pendingLinks || 0}
            subtitle="In queue"
            icon={<Schedule />}
            color="warning"
            loading={statsLoading}
          />
        </Grid>

        {/* Failed Links */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Failed"
            value={stats?.failedLinks || 0}
            subtitle="Processing errors"
            icon={<Error />}
            color="error"
            loading={statsLoading}
            trend={stats?.failedLinks && stats.failedLinks > 0 ? 'down' : 'flat'}
            trendValue={`${(((stats?.failedLinks || 0) / Math.max(stats?.totalLinks || 1, 1)) * 100).toFixed(1)}% error rate`}
          />
        </Grid>

        {/* Processing Progress */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Speed color="primary" />
                <Typography variant="h6">Processing Progress</Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" color="textSecondary">
                    Overall Progress
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {getProcessingRate().toFixed(1)}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={getProcessingRate()}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" color="textSecondary">
                    Success Rate
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {getSuccessRate().toFixed(1)}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={getSuccessRate()}
                  color="success"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Domain Statistics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Storage color="primary" />
                <Typography variant="h6">Domain Statistics</Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {stats?.totalDomains || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total Domains
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary">
                      {stats ? Math.round(stats.totalLinks / Math.max(stats.totalDomains, 1)) : 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Avg Links/Domain
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RealTimeStats;
