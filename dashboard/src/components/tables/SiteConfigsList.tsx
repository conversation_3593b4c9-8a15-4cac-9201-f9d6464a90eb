import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  IconButton,
  <PERSON>lt<PERSON>,
  <PERSON>ton,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
} from '@mui/material';
import { Edit, Delete, Add, Visibility, Code, Domain } from '@mui/icons-material';
import type { SiteConfig } from '../../types/api';

interface SiteConfigData {
  schema: {
    name: string;
    fields: Array<{
      name: string;
      type: string;
      selector: string;
      attribute?: string;
    }>;
  };
  crawl_delay: number;
  max_depth: number;
  follow_external_links: boolean;
}

interface SiteConfigsListProps {
  configs: SiteConfig[];
  loading?: boolean;
  onEdit: (config: SiteConfig) => void;
  onDelete: (domain: string) => void;
  onAdd: () => void;
  onView: (config: SiteConfig) => void;
}

const SiteConfigsList: React.FC<SiteConfigsListProps> = ({
  configs,
  loading = false,
  onEdit,
  onDelete,
  onAdd,
  onView: _onView, // Prefix with underscore to indicate intentionally unused
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [configToDelete, setConfigToDelete] = useState<string | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [configToView, setConfigToView] = useState<SiteConfig | null>(null);

  const handleDeleteClick = (domain: string) => {
    setConfigToDelete(domain);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (configToDelete) {
      onDelete(configToDelete);
      setDeleteDialogOpen(false);
      setConfigToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setConfigToDelete(null);
  };

  const handleViewClick = (config: SiteConfig) => {
    setConfigToView(config);
    setViewDialogOpen(true);
  };

  const handleViewClose = () => {
    setViewDialogOpen(false);
    setConfigToView(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getFieldCount = (config: unknown): number => {
    if (!config) return 0;
    const typedConfig = config as SiteConfigData;
    return typedConfig.schema?.fields?.length || 0;
  };

  const getCrawlDelay = (config: unknown): number => {
    if (!config) return 1.0;
    const typedConfig = config as SiteConfigData;
    return typedConfig.crawl_delay ?? 1.0;
  };

  const getMaxDepth = (config: unknown): number => {
    if (!config) return 3;
    const typedConfig = config as SiteConfigData;
    return typedConfig.max_depth ?? 3;
  };

  if (loading) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body1" color="textSecondary">
          Loading configurations...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">Site Configurations ({configs.length})</Typography>
        <Button variant="contained" startIcon={<Add />} onClick={onAdd}>
          Add Configuration
        </Button>
      </Box>

      {/* Configurations Grid */}
      {configs.length === 0 ? (
        <Alert severity="info">
          No site configurations found. Add a configuration to get started.
        </Alert>
      ) : (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '24px', width: '100%' }}>
          {configs.map(config => (
            <div key={config.domain} style={{ width: 'calc(33.333% - 16px)' }}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  {/* Domain Header */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Domain color="primary" />
                    <Typography variant="h6" noWrap>
                      {config.domain}
                    </Typography>
                  </Box>

                  {/* Configuration Details */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      Schema: {config.config?.schema?.name || 'Unnamed Schema'}
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
                      <Chip
                        label={`${getFieldCount(config.config)} fields`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={`${getCrawlDelay(config.config)}s delay`}
                        size="small"
                        color="secondary"
                        variant="outlined"
                      />
                      <Chip
                        label={`depth ${getMaxDepth(config.config)}`}
                        size="small"
                        color="info"
                        variant="outlined"
                      />
                    </Box>
                  </Box>

                  {/* Timestamps */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="caption" color="textSecondary" display="block">
                      Created: {formatDate(config.created_at)}
                    </Typography>
                    <Typography variant="caption" color="textSecondary" display="block">
                      Updated: {formatDate(config.updated_at)}
                    </Typography>
                  </Box>

                  {/* Actions */}
                  <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
                    <Tooltip title="View Configuration">
                      <IconButton size="small" onClick={() => handleViewClick(config)}>
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Configuration">
                      <IconButton size="small" onClick={() => onEdit(config)} color="primary">
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Configuration">
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteClick(config.domain)}
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteCancel}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the configuration for "{configToDelete}"? This action
            cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Configuration Dialog */}
      <Dialog open={viewDialogOpen} onClose={handleViewClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Code />
            Configuration: {configToView?.domain}
          </Box>
        </DialogTitle>
        <DialogContent>
          {configToView && (
            <TextField
              fullWidth
              multiline
              rows={20}
              value={JSON.stringify(configToView.config, null, 2)}
              InputProps={{
                readOnly: true,
                sx: { fontFamily: 'monospace', fontSize: '0.875rem' },
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleViewClose}>Close</Button>
          <Button
            onClick={() => configToView && onEdit(configToView)}
            variant="contained"
            startIcon={<Edit />}
          >
            Edit
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SiteConfigsList;
