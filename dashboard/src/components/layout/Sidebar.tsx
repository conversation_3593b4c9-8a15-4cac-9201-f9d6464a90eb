import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Chip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Link as LinkIcon,
  Settings as SettingsIcon,
  BarChart as BarChartIcon,
  HealthAndSafety as HealthIcon,
  Storage as StorageIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useRealTimeHealth } from '../../hooks/useApi';

interface SidebarProps {
  onItemClick?: () => void;
}

interface NavigationItem {
  text: string;
  icon: React.ReactElement;
  path: string;
  badge?: string | number;
}

const Sidebar: React.FC<SidebarProps> = ({ onItemClick }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { data: health } = useRealTimeHealth(30000);

  const navigationItems: NavigationItem[] = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/',
    },
    {
      text: 'Links',
      icon: <LinkIcon />,
      path: '/links',
    },
    {
      text: 'Analytics',
      icon: <BarChartIcon />,
      path: '/analytics',
    },
    {
      text: 'Site Configs',
      icon: <SettingsIcon />,
      path: '/configs',
    },
    {
      text: 'System Health',
      icon: <HealthIcon />,
      path: '/health',
    },
    {
      text: 'Data Storage',
      icon: <StorageIcon />,
      path: '/storage',
    },
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
    onItemClick?.();
  };

  const getHealthColor = () => {
    if (!health) return 'default';
    return health.status === 'healthy' ? 'success' : 'error';
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Title */}
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DashboardIcon color="primary" />
          <Typography variant="h6" noWrap component="div">
            Crawler
          </Typography>
        </Box>
      </Toolbar>

      <Divider />

      {/* System Status */}
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            System Status
          </Typography>
          <Chip
            size="small"
            label={health?.status || 'Unknown'}
            color={getHealthColor()}
            variant="outlined"
          />
        </Box>
        {health && (
          <Typography variant="caption" color="text.secondary">
            Last check: {new Date(health.timestamp).toLocaleTimeString()}
          </Typography>
        )}
      </Box>

      <Divider />

      {/* Navigation */}
      <List sx={{ flexGrow: 1, pt: 1 }}>
        {navigationItems.map(item => {
          const isActive = location.pathname === item.path;

          return (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={isActive}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  mx: 1,
                  borderRadius: 1,
                  '&.Mui-selected': {
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? 'inherit' : 'text.secondary',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive ? 500 : 400,
                  }}
                />
                {item.badge && (
                  <Chip size="small" label={item.badge} color="secondary" sx={{ ml: 1 }} />
                )}
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      <Divider />

      {/* Footer */}
      <Box sx={{ p: 2 }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Web Crawler v1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          © 2024 Crawler Dashboard
        </Typography>
      </Box>
    </Box>
  );
};

export default Sidebar;
