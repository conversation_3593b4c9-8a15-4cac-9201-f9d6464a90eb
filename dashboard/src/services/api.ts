import axios, { type AxiosInstance, type AxiosR<PERSON>ponse, AxiosError } from 'axios';
import type {
  Link,
  SiteConfig,
  DiscoveryRequest,
  DiscoveryResponse,
  HealthCheck,
  ReadinessCheck,
  LivenessCheck,
  LinksQueryParams,
  ApiError,
} from '../types/api';

class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor(baseURL?: string) {
    this.baseURL = baseURL || import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      config => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      error => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error: AxiosError<ApiError>) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: AxiosError<ApiError>): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.detail || `HTTP ${error.response.status}`;
      return new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection');
    } else {
      // Something else happened
      return new Error(error.message || 'An unexpected error occurred');
    }
  }

  // Health endpoints
  async getHealth(): Promise<HealthCheck> {
    const response = await this.client.get<HealthCheck>('/health');
    return response.data;
  }

  async getReadiness(): Promise<ReadinessCheck> {
    const response = await this.client.get<ReadinessCheck>('/health/ready');
    return response.data;
  }

  async getLiveness(): Promise<LivenessCheck> {
    const response = await this.client.get<LivenessCheck>('/health/live');
    return response.data;
  }

  // Discovery endpoints
  async discoverLinks(request: DiscoveryRequest): Promise<DiscoveryResponse> {
    const response = await this.client.post<DiscoveryResponse>('/api/v1/discover', request);
    return response.data;
  }

  // Links endpoints
  async getLinks(params?: LinksQueryParams): Promise<Link[]> {
    const response = await this.client.get<Link[]>('/api/v1/links', { params });
    return response.data;
  }

  // Site configuration endpoints
  async getSiteConfig(domain: string): Promise<SiteConfig> {
    const response = await this.client.get<SiteConfig>(`/api/v1/sites/${domain}/config`);
    return response.data;
  }

  async updateSiteConfig(domain: string, config: Record<string, any>): Promise<SiteConfig> {
    const response = await this.client.put<SiteConfig>(`/api/v1/sites/${domain}/config`, config);
    return response.data;
  }

  // Utility methods
  setBaseURL(url: string): void {
    this.baseURL = url;
    this.client.defaults.baseURL = url;
  }

  getBaseURL(): string {
    return this.baseURL;
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;
