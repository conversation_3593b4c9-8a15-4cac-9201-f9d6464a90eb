import { type Link, LinkStatus } from '../types/api';
import type {
  DashboardStats,
  DomainStats,
  CrawlingMetrics,
  TimeSeriesData,
  ChartDataPoint,
} from '../types/dashboard';
import { format, subHours, subDays } from 'date-fns';

export class StatisticsService {
  /**
   * Calculate dashboard statistics from links data
   */
  static calculateDashboardStats(links: Link[]): DashboardStats {
    const totalLinks = links.length;
    const pendingLinks = links.filter(link => link.status === LinkStatus.PENDING).length;
    const crawlingLinks = links.filter(link => link.status === LinkStatus.CRAWLING).length;
    const completedLinks = links.filter(link => link.status === LinkStatus.COMPLETED).length;
    const failedLinks = links.filter(link => link.status === LinkStatus.FAILED).length;

    const uniqueDomains = new Set(links.map(link => link.domain));
    const totalDomains = uniqueDomains.size;

    const successRate =
      totalLinks > 0 ? (completedLinks / (completedLinks + failedLinks)) * 100 : 0;

    // Calculate average processing time (mock calculation)
    const averageProcessingTime = this.calculateAverageProcessingTime(links);

    return {
      totalLinks,
      pendingLinks,
      crawlingLinks,
      completedLinks,
      failedLinks,
      totalDomains,
      successRate: Math.round(successRate * 100) / 100,
      averageProcessingTime,
    };
  }

  /**
   * Calculate domain-specific statistics
   */
  static calculateDomainStats(links: Link[]): DomainStats[] {
    const domainMap = new Map<string, Link[]>();

    // Group links by domain
    links.forEach(link => {
      if (!domainMap.has(link.domain)) {
        domainMap.set(link.domain, []);
      }
      domainMap.get(link.domain)!.push(link);
    });

    // Calculate stats for each domain
    return Array.from(domainMap.entries())
      .map(([domain, domainLinks]) => {
        const totalLinks = domainLinks.length;
        const completedLinks = domainLinks.filter(
          link => link.status === LinkStatus.COMPLETED
        ).length;
        const failedLinks = domainLinks.filter(link => link.status === LinkStatus.FAILED).length;
        const successRate =
          totalLinks > 0 ? (completedLinks / (completedLinks + failedLinks)) * 100 : 0;

        return {
          domain,
          totalLinks,
          completedLinks,
          failedLinks,
          successRate: Math.round(successRate * 100) / 100,
        };
      })
      .sort((a, b) => b.totalLinks - a.totalLinks);
  }

  /**
   * Generate time series data for charts
   */
  static generateTimeSeriesData(links: Link[], hours: number = 24): TimeSeriesData[] {
    const now = new Date();
    const timePoints: ChartDataPoint[] = [];

    // Generate hourly data points
    for (let i = hours; i >= 0; i--) {
      const timestamp = subHours(now, i);
      const timestampStr = format(timestamp, 'yyyy-MM-dd HH:00:00');

      // Count links created in this hour
      const linksInHour = links.filter(link => {
        const linkTime = new Date(link.created_at);
        return linkTime >= subHours(timestamp, 1) && linkTime < timestamp;
      }).length;

      timePoints.push({
        timestamp: format(timestamp, 'HH:mm'),
        value: linksInHour,
      });
    }

    return [
      {
        name: 'Links Discovered',
        data: timePoints,
        color: '#1976d2',
      },
    ];
  }

  /**
   * Generate status distribution data for pie charts
   */
  static generateStatusDistribution(links: Link[]): ChartDataPoint[] {
    const statusCounts = {
      [LinkStatus.PENDING]: 0,
      [LinkStatus.CRAWLING]: 0,
      [LinkStatus.COMPLETED]: 0,
      [LinkStatus.FAILED]: 0,
    };

    links.forEach(link => {
      statusCounts[link.status]++;
    });

    return Object.entries(statusCounts).map(([status, count]) => ({
      timestamp: status,
      value: count,
      label: status.charAt(0).toUpperCase() + status.slice(1),
    }));
  }

  /**
   * Calculate crawling metrics
   */
  static calculateCrawlingMetrics(links: Link[]): CrawlingMetrics {
    const recentLinks = links.filter(link => {
      const linkTime = new Date(link.created_at);
      return linkTime >= subHours(new Date(), 1);
    });

    const linksPerHour = recentLinks.length;
    const averageResponseTime = this.calculateAverageProcessingTime(recentLinks);
    const errorRate = this.calculateErrorRate(links);
    const queueDepth = links.filter(link => link.status === LinkStatus.PENDING).length;
    const activeWorkers = links.filter(link => link.status === LinkStatus.CRAWLING).length;

    return {
      linksPerHour,
      averageResponseTime,
      errorRate,
      queueDepth,
      activeWorkers,
    };
  }

  /**
   * Calculate average processing time (mock implementation)
   */
  private static calculateAverageProcessingTime(links: Link[]): number {
    const completedLinks = links.filter(link => link.status === LinkStatus.COMPLETED);
    if (completedLinks.length === 0) return 0;

    // Mock calculation - in real implementation, you'd calculate based on actual timestamps
    return Math.random() * 5000 + 1000; // Random between 1-6 seconds
  }

  /**
   * Calculate error rate
   */
  private static calculateErrorRate(links: Link[]): number {
    const processedLinks = links.filter(
      link => link.status === LinkStatus.COMPLETED || link.status === LinkStatus.FAILED
    );

    if (processedLinks.length === 0) return 0;

    const failedLinks = links.filter(link => link.status === LinkStatus.FAILED);
    return (failedLinks.length / processedLinks.length) * 100;
  }
}

export default StatisticsService;
