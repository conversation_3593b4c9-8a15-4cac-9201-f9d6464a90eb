# Development Environment Configuration

# Database
POSTGRES_SERVER=localhost
POSTGRES_USER=crawl_user
POSTGRES_PASSWORD=crawl_dev_password
POSTGRES_DB=crawl_db
POSTGRES_PORT=5432

# Message Broker
RABBITMQ_USER=crawl_user
RABBITMQ_PASSWORD=crawl_dev_password
RABBITMQ_VHOST=crawl_vhost
BROKER_URL=pyamqp://crawl_user:crawl_dev_password@localhost:5672/crawl_vhost
RESULT_BACKEND=redis://localhost:6379/0

# Application
DEBUG=true
LOG_LEVEL=DEBUG
API_V1_PREFIX=/api/v1

# Crawling
USER_AGENT=CrawlBot/1.0 (+https://example.com/bot)
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=60
MAX_CONCURRENT_REQUESTS=10
RESPECT_ROBOTS_TXT=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Security
SECRET_KEY=dev_secret_key_change_in_production
