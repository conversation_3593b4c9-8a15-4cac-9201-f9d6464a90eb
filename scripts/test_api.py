#!/usr/bin/env python3
"""
Test script for the web crawler API.
"""

import requests
import json
import time
import sys
from typing import Dict, Any


class CrawlerAPITest:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health(self) -> bool:
        """Test health endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def test_discover_links(self) -> bool:
        """Test link discovery endpoint."""
        try:
            payload = {
                "urls": ["https://httpbin.org/html"],
                "max_depth": 1,
                "priority": 0
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/discover",
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Link discovery successful: {data['message']}")
                return True
            else:
                print(f"❌ Link discovery failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Link discovery error: {e}")
            return False
    
    def test_list_links(self) -> bool:
        """Test list links endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/links?limit=10")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ List links successful: {len(data)} links found")
                return True
            else:
                print(f"❌ List links failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ List links error: {e}")
            return False
    
    def test_site_config(self) -> bool:
        """Test site configuration endpoints."""
        try:
            domain = "example.com"
            config = {
                "schema": {
                    "name": "Test Schema",
                    "baseSelector": "body",
                    "fields": [
                        {"name": "title", "selector": "h1", "type": "text"},
                        {"name": "content", "selector": "p", "type": "text"}
                    ]
                }
            }
            
            # Update config
            response = self.session.put(
                f"{self.base_url}/api/v1/sites/{domain}/config",
                json=config
            )
            
            if response.status_code == 200:
                print("✅ Site config update successful")
                
                # Get config
                response = self.session.get(f"{self.base_url}/api/v1/sites/{domain}/config")
                if response.status_code == 200:
                    print("✅ Site config retrieval successful")
                    return True
                else:
                    print(f"❌ Site config retrieval failed: {response.status_code}")
                    return False
            else:
                print(f"❌ Site config update failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Site config error: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all tests."""
        print("🚀 Starting API tests...\n")
        
        tests = [
            ("Health Check", self.test_health),
            ("Link Discovery", self.test_discover_links),
            ("List Links", self.test_list_links),
            ("Site Configuration", self.test_site_config),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"Running {test_name}...")
            if test_func():
                passed += 1
            print()
        
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed!")
            return True
        else:
            print("❌ Some tests failed!")
            return False


def main():
    """Main function."""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    
    print(f"Testing API at: {base_url}")
    
    tester = CrawlerAPITest(base_url)
    
    # Wait for services to be ready
    print("⏳ Waiting for services to be ready...")
    for i in range(30):
        if tester.test_health():
            break
        time.sleep(2)
    else:
        print("❌ Services not ready after 60 seconds")
        sys.exit(1)
    
    # Run tests
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
