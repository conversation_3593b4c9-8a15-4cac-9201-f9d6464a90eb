-- Database initialization script for web crawler

-- Create database if it doesn't exist
-- Note: This is handled by Docker environment variables

-- Site-specific crawling configurations
CREATE TABLE IF NOT EXISTS site_configs (
    domain TEXT PRIMARY KEY,
    config JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Discovered links queue and status tracking
CREATE TABLE IF NOT EXISTS links (
    id SERIAL PRIMARY KEY,
    url TEXT UNIQUE NOT NULL,
    domain TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    status TEXT DEFAULT 'pending',
    priority INTEGER DEFAULT 0,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crawled page content storage
CREATE TABLE IF NOT EXISTS pages (
    id SERIAL PRIMARY KEY,
    url TEXT UNIQUE NOT NULL,
    title TEXT,
    content JSONB NOT NULL,
    extracted_data JSONB DEFAULT '{}',
    crawled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    content_hash TEXT,
    status_code INTEGER
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_links_url ON links(url);
CREATE INDEX IF NOT EXISTS idx_links_domain ON links(domain);
CREATE INDEX IF NOT EXISTS idx_links_status ON links(status);
CREATE INDEX IF NOT EXISTS idx_links_priority ON links(priority);
CREATE INDEX IF NOT EXISTS idx_links_created_at ON links(created_at);

CREATE INDEX IF NOT EXISTS idx_pages_url ON pages(url);
CREATE INDEX IF NOT EXISTS idx_pages_content_hash ON pages(content_hash);
CREATE INDEX IF NOT EXISTS idx_pages_crawled_at ON pages(crawled_at);

CREATE INDEX IF NOT EXISTS idx_site_configs_domain ON site_configs(domain);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_site_configs_updated_at ON site_configs;
CREATE TRIGGER update_site_configs_updated_at
    BEFORE UPDATE ON site_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_links_updated_at ON links;
CREATE TRIGGER update_links_updated_at
    BEFORE UPDATE ON links
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default site configurations
INSERT INTO site_configs (domain, config) VALUES 
(
    'example.com',
    '{
        "schema": {
            "name": "Example Site Schema",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "title",
                    "selector": "h1, .title, title",
                    "type": "text"
                },
                {
                    "name": "content",
                    "selector": ".content, .article-body, main p",
                    "type": "text"
                },
                {
                    "name": "author",
                    "selector": ".author, .byline",
                    "type": "text"
                },
                {
                    "name": "date",
                    "selector": ".date, .published, time",
                    "type": "text"
                },
                {
                    "name": "links",
                    "selector": "a[href]",
                    "type": "attribute",
                    "attribute": "href"
                }
            ]
        },
        "crawl_delay": 1.0,
        "max_depth": 3,
        "follow_external_links": false
    }'
) ON CONFLICT (domain) DO NOTHING;

-- Create a view for crawl statistics
CREATE OR REPLACE VIEW crawl_stats AS
SELECT 
    COUNT(*) as total_links,
    COUNT(*) FILTER (WHERE status = 'pending') as pending_links,
    COUNT(*) FILTER (WHERE status = 'crawling') as crawling_links,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_links,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_links,
    COUNT(DISTINCT domain) as unique_domains,
    (SELECT COUNT(*) FROM pages) as total_pages
FROM links;
